# Memory Bank File: activeContext.md

## Current Work Focus
**Recently completed: Implemented Pinterest CSV bulk upload functionality in SEO tool.** Replaced Pinterest share modal with CSV download functionality that generates files compliant with Pinterest's bulk upload requirements. The system now creates downloadable CSV files with proper formatting for Pinterest Business accounts to upload up to 200 products at once. CSV includes all required fields: Title, Media URL, Pinterest board, Description, Link, and Keywords.

**Previously completed: Fixed search functionality to match exact words instead of substrings, including special characters and comma-separated keywords.** Resolved issue where searching for "bust" would return results containing "robust" and "robuste" by implementing comprehensive word boundary matching. Also fixed issue where searching for "cão" (with special characters) in SEO keywords wasn't working due to comma-separated format. Updated three search functions to use multiple LIKE patterns that handle both space and comma word boundaries, eliminating unwanted substring matches while ensuring proper matching of SEO keywords.

**Previously completed: Optimized IP-to-country lookup system by migrating from CSV to SQLite database.** Successfully replaced CSV file parsing in `get_country_from_ip()` function with SQLite database queries, improving performance and scalability. The system now uses `IPs.sqlite` with 136,595 IP range records and proper indexing for fast lookups. Thoroughly tested with multiple IP addresses confirming accurate country code detection.

**Previously completed: Fixed banner management blank page issue and cleaned up orphaned banner images.** Resolved blocking UI issue during banner updates/deletions by removing synchronous cleanup calls and created standalone cleanup script that successfully reduced orphaned images from 12 to 4 files.

**Previously completed: Fixed session data deserialization issue causing missing cart contents on admin sessions page.** Resolved PHP `unserialize()` warnings in error logs and implemented robust error handling for various session data formats. This ensures administrators can properly view cart contents in the session management interface.

**Previously completed: Implemented IP tracking and country flag display for admin sessions management.** Added user IP address tracking to the sessions table with database migration, updated session management functions to capture and store IP addresses, and enhanced the admin sessions interface to display country flags next to IP addresses with hover tooltips showing country names.

## Recent Changes (Key Completions)
*   **Pinterest CSV Bulk Upload Generator (Latest):**
    *   **Functionality Change:** Replaced Pinterest share modal with CSV download functionality for Pinterest Business bulk upload.
    *   **Pinterest Requirements Compliance:** Generated CSV follows Pinterest's technical specifications from https://help.pinterest.com/pt-pt/business/article/bulk-upload-video-pins
    *   **CSV Structure:** Includes all required and optional fields:
        *   **Title** (required): Product name truncated to 100 characters max
        *   **URL de multimédia** (required): Full public URL to product image
        *   **Pinterest board** (required): Default "JCS Studio Products" board name
        *   **Descrição** (optional): SEO description or product description, max 500 characters
        *   **Link** (optional): Full product URL with slug format
        *   **Keywords** (optional): SEO keywords comma-separated
    *   **Technical Implementation:**
        *   CSV generation with proper escaping for commas, quotes, and newlines
        *   Automatic file download using Blob API
        *   Fetches up to 1000 products from database
        *   Uses `https://joaocesarsilva.com/` base URL (without www)
        *   Proper image URLs pointing to `/public/assets/images/products/`
    *   **User Experience:** Single button click generates and downloads ready-to-use CSV file for Pinterest Business upload
    *   **File Output:** `pinterest_products.csv` with all active products formatted for Pinterest bulk upload

*   **Pinterest Product Sharing in SEO Tool (Previous):**
    *   **Feature Implementation:** Added product list modal to SEO tool (`public_html/SEO/index.html`) with Pinterest share functionality for each product.
    *   **Product Retrieval System:** Created `public_html/SEO/get_products.php` to fetch products from SQLite database with pagination support (100 products per page, option to load all up to 1000).
    *   **Database Integration:** Retrieves products with images, SEO data (title, description, keywords), pricing, and generates proper product URLs for sharing.
    *   **Pinterest Share Fix:** Resolved issue where product names, descriptions, and SEO keywords with special characters weren't being transferred to Pinterest share.
        *   **Root Cause:** Inline onclick handlers with escaped quotes were causing JavaScript parsing issues.
        *   **Solution:** Replaced inline onclick with data attributes (`data-name`, `data-description`, `data-keywords`, etc.) and event listeners.
        *   **Data Handling:** Uses HTML entity encoding (`&quot;`) for data attributes and proper URL construction for Pinterest API.
    *   **Pinterest Integration:** Generates Pinterest share URLs with:
        *   Product URL (full domain: `https://www.joaocesarsilva.com/index.php?product_id=X`)
        *   Product image (full path: `/public/assets/images/products/filename`)
        *   Description with SEO keywords converted to hashtags (comma-separated keywords become space-separated hashtags)
    *   **UI Enhancements:** Added responsive product cards with hover effects, pagination controls, and "Load All Products" functionality.
    *   **Image Path Correction:** Fixed image paths from `../imgv2/` to `/public/assets/images/products/` to match actual file structure.
    *   **Technical Implementation:**
        *   Modal with Bootstrap integration
        *   AJAX product loading with error handling
        *   Event delegation for dynamically created Pinterest buttons
        *   Pagination with previous/next controls and product count display
        *   CSS styling with Pinterest brand colors and responsive design

*   **Search Functionality Word Boundary Fix with Special Characters Support:**
    *   **Issue Resolution:** Fixed search functionality returning unwanted substring matches (e.g., searching "bust" returned "robust" and "robuste") and special character keyword matching (e.g., searching "cão" wasn't finding products with that SEO keyword).
    *   **Root Cause Analysis:**
        *   Search functions used `%searchterm%` LIKE patterns which match any substring occurrence, not just whole words.
        *   SEO keywords are comma-separated (e.g., "doberman, cão, vinil") but search only checked for space boundaries.
    *   **Comprehensive Solution:** Implemented enhanced word boundary matching using 10 LIKE patterns per field:
        *   **Space boundaries:** `% searchterm %`, `searchterm %`, `% searchterm`, `searchterm`
        *   **Comma boundaries:** `%, searchterm, %`, `searchterm, %`, `%, searchterm`, `%,searchterm,%`, `searchterm,%`, `%,searchterm`
    *   **Functions Updated:**
        *   `search_site_content()` in `includes/functions.php` - main site search (60 parameters total)
        *   `search_site_content_paginated()` in `includes/functions.php` - paginated site search (60 parameters total)
        *   `get_blog_posts()` in `includes/blog_functions.php` - blog search functionality (30 parameters total)
    *   **Technical Implementation:** Each search field (name, description, SKU, keywords, title, content) now uses 10 LIKE conditions with proper parameter binding to handle both space and comma word boundaries.
    *   **Special Characters Support:** Properly handles Portuguese characters (ã, ç, etc.) in search terms and database content.
    *   **SEO Keywords Integration:** Confirmed working with comma-separated SEO keywords stored in `products.seo_keywords` field.
    *   **Testing Verified:** Searching "cão" now correctly returns products with that keyword in SEO fields.
    *   **Backward Compatibility:** Maintains all existing search functionality while eliminating false positive substring matches.
    *   **Performance Consideration:** Uses 10 LIKE conditions per field but maintains efficient database queries with proper indexing.

*   **IP-to-Country Lookup System Optimization:**
    *   **Performance Enhancement:** Migrated `get_country_from_ip()` function from CSV file parsing to SQLite database queries for significantly improved lookup performance.
    *   **Database Implementation:** Replaced `asn-country-ipv4.csv` file reading with `IPs.sqlite` database containing 136,595 IP range records in optimized table structure.
    *   **Function Modernization:** Updated `includes/functions.php` line 2531+ to use PDO database connections with proper error handling and resource management.
    *   **Query Optimization:** Implemented string-based IP range comparison with composite indexing (`idx_ip_range` on `Starting_IP` and `Ending_IP` columns) for fast lookups.
    *   **Comprehensive Testing:** Verified functionality with multiple IP addresses including Google DNS (*******→US), Portuguese IPs (**********→PT), and session-based IPs, all returning accurate country codes.
    *   **Scalability Improvement:** Database approach handles large IP datasets more efficiently than CSV parsing, with automatic indexing and query plan optimization.
    *   **Integration Success:** Seamlessly integrated with existing session management and admin interface country flag display functionality.

*   **Banner Management Blank Page Fix & Orphaned Image Cleanup:**
    *   **Issue Resolution:** Fixed blank page occurring after banner updates and deletions in the admin interface.
    *   **Root Cause:** Synchronous calls to `cleanup_orphaned_banner_images()` function in `banners.php` were blocking the UI and causing the page to hang.
    *   **Solution Implementation:**
        *   Removed blocking calls to `cleanup_orphaned_banner_images()` from both banner update and delete actions in `banners.php`.
        *   Modified redirect logic to use `redirect_tab` POST parameter with fallback to 'list' tab.
        *   Created standalone `simple_cleanup.php` script for independent orphaned image cleanup.
    *   **Cleanup Results:** Successfully executed cleanup script, reducing banner directory files from 12 to 4 images, removing 8 orphaned files.
    *   **Performance Improvement:** Banner management system now operates without UI blocking, providing immediate feedback to administrators.
    *   **Maintenance Recommendation:** Run `simple_cleanup.php` periodically to maintain clean banner directory.

*   **Session Data Deserialization Fix:**
    *   **Issue Resolution:** Fixed missing cart contents display on admin sessions page under "Conteúdo do Carrinho (Sumário)" section.
    *   **Error Log Cleanup:** Eliminated multiple PHP warnings related to `unserialize(): Error at offset 0` in `includes/session.php` line 462.
    *   **Robust Error Handling:** Enhanced `get_all_sessions_data()` function with comprehensive error handling using `@unserialize()` and fallback logic for pipe-separated session data formats.
    *   **Backward Compatibility:** Maintained support for existing session formats while handling corrupted or malformed data gracefully.
    *   **Admin Interface:** Cart contents now display correctly in session management, providing complete session information for monitoring.

*   **IP Tracking and Country Flag Display for Sessions (Latest):**
    *   **Database Enhancement:** Added `user_ip` column to `sessions` table via migration `add_user_ip_to_sessions.php` to track user IP addresses for each session.
    *   **Session Management Updates:** Modified `write_session()` function in `includes/session.php` to capture and store user IP using `get_customer_ip()` function. Updated `get_all_sessions_data()` to include IP address in returned session data.
    *   **Admin Interface Enhancement:** Updated `templates/backend/sessions_list.php` to display user IP addresses with country flag icons and hover tooltips showing country names.
    *   **Flag Icons Integration:** Added Flag Icons CSS library via CDN in `header.php` for displaying country flags using ISO country codes.
    *   **Geolocation Service:** Implemented IP-to-country detection using the free `country.is` API with JavaScript caching, error handling, and fallback for invalid/private IP addresses.
    *   **Legacy Data Handling:** Updated existing sessions with 'LEGACY_SESSION' placeholder for user_ip where previously NULL.
    *   **User Experience:** Provides administrators with immediate visual identification of session geographic origins and enhances security monitoring capabilities.

*   **Admin Sitemaps Navigation Non-AJAX Fix:**
    *   **Issue Resolved:** Fixed sitemaps sidebar navigation showing dashboard when accessed via direct URL refresh.
    *   **Root Cause:** Missing 'sitemaps' case in main switch statement in `admin.php` that handles non-AJAX GET requests (line ~5900+).
    *   **Solution:** Added proper sitemaps case to main handler with page title, sitemap functions inclusion, table creation check, and template inclusion logic.
    *   **Database Verification:** Confirmed `sitemap_configs` table exists with proper schema. The `check_and_create_sitemap_configs_table()` function in `db.php` ensures table creation/updates, so no manual database changes were needed.
    *   **Pattern Recognition:** This was the inverse of issue #25 with 'banners' - where banners was missing from AJAX handler, sitemaps was missing from the main (non-AJAX) handler.
    *   **Prevention:** Both AJAX and non-AJAX switch statements must be kept in sync for new admin sections. Always test both direct URL access and sidebar navigation.

*   **Admin Banners Navigation AJAX Fix:**
    *   **Issue Resolved:** Fixed banners sidebar navigation showing dashboard until refresh.
    *   **Root Cause:** Missing 'banners' case in AJAX switch statement in `admin.php` (line ~1273).
    *   **Solution:** Added proper banners case to AJAX handler matching the existing non-AJAX case.
    *   **Pattern Recognition:** This was identical to issue #19 with 'digital_products' - a recurring pattern when adding new admin sections.
    *   **Prevention:** Both AJAX and non-AJAX switch statements must be kept in sync for new admin sections.

*   **License Verification System Improvements:**
    *   **Fixed License Verification Download Page Issues:**
        *   **Separate Token System:** Created dedicated `license_verification_tokens` table with 15-minute expiry to prevent conflicts with regular download tokens.
        *   **Step-by-Step Verification:** Implemented proper verification flow without automatic downloads - users must explicitly click download after verification.
        *   **Email Censoring:** Updated to show `e*****@*****.c**` format for unverified users and normal censoring for verified users.
        *   **Hidden Details:** "Validade do Download" and "Downloads" fields only shown after successful verification.
        *   **Template:** Created `templates/frontend/download_fixed.php` with proper UI flow and security measures.
        *   **Functions:** Added `create_license_verification_token()`, `verify_license_verification_token()`, `cleanup_expired_license_verification_tokens()` in `includes/digital_product_functions.php`.
        *   **Database Migration:** Added migration `includes/db_migrations/create_license_verification_tokens.php` to create the new table structure.
        *   **Updated Index:** Modified `index.php` to use the new `download_fixed.php` template.
    *   **Configurable Email Templates & Order Details:**
        *   **Email Template Setting:** Added `license_verification_email_template` setting to admin settings with placeholders: `{customer_name}`, `{license_code}`, `{verification_code}`.
        *   **Order Details Email:** Added button to send full order details email after successful verification (no re-verification needed).
        *   **Settings Integration:** Added new template to `templates/backend/settings.php` and `includes/settings_handler.php` as allowed setting.
        *   **Template Usage:** Updated verification email sending to use configurable template instead of hardcoded text.
    *   **Maintenance System Enhancements:**
        *   **License Verification Token Maintenance:** Added comprehensive maintenance functions in `includes/maintenance_functions.php`:
            *   `cleanup_expired_license_verification_tokens()` - Remove expired tokens
            *   `cleanup_all_license_verification_tokens()` - Remove all tokens
            *   `cleanup_old_used_license_verification_tokens()` - Remove used tokens older than 1 hour
            *   `get_license_verification_tokens_stats()` - Get statistics for admin display
        *   **Admin UI:** Added "Tokens de Verificação de Licença" section to `templates/backend/maintenance_simple.php` with statistics and cleanup actions.
        *   **Admin Handlers:** Added action handlers in `admin.php` for all new maintenance actions with proper flash messages.

*   **SKU Standardization & Free Digital Product Labeling:**
    *   **SKU Length Standardization:**
        *   Updated `generate_random_sku()` function in [`includes/product_functions.php`](includes/product_functions.php:304) to generate 10-character SKUs for all product types (previously 5 for simple, 10 for digital).
        *   Updated `generate_unique_sku()` function to use consistent 10-character logic for collision resolution.
        *   Modified validation in [`admin.php`](admin.php:2055) to require exactly 10 characters for all product SKUs.
        *   Updated product form template [`templates/backend/product_form.php`](templates/backend/product_form.php:174) to reflect 10-character requirement.
        *   Created migration script [`includes/db_migrations/update_skus_to_10_characters.php`](includes/db_migrations/update_skus_to_10_characters.php:1) to update existing products with short SKUs.
        *   Successfully migrated 17 existing products from 5-character to 10-character SKUs.
    *   **Free Digital Product "GRÁTIS" Label:**
        *   Added visual "GRÁTIS" label in [`templates/frontend/partials/product_card.php`](templates/frontend/partials/product_card.php:142) for digital products with `base_price = 0`.
        *   Label appears in bottom-left corner of product thumbnails with green background and white text.
        *   Free digital products maintain normal cart and checkout functionality (no special handling required).
        *   Styling uses Tailwind CSS classes for responsive design and proper visual hierarchy.
*   **Admin Maintenance: Token & Session Management Enhancements:**
    *   **Token Clearing:**
        *   Added an option to "Limpar Tokens de Download ATIVOS (Não Expirados)" in the `admin.php?section=maintenance` page. This action deletes all download tokens (`download_tokens` table) that have not yet expired. Implemented via `cleanup_all_active_download_tokens()` in [`includes/maintenance_functions.php`](includes/maintenance_functions.php:38).
        *   Added an option to "Limpar Tokens de Acesso ATIVOS (Não Expirados)" in the `admin.php?section=maintenance` page. This action deletes all order access tokens (`order_access_tokens` table) that have not yet expired. Implemented via `cleanup_all_active_order_access_tokens()` in [`includes/maintenance_functions.php`](includes/maintenance_functions.php:63).
        *   Updated [`templates/backend/maintenance_simple.php`](templates/backend/maintenance_simple.php:1) with UI buttons for these actions.
        *   Updated [`admin.php`](admin.php:1) to handle these new POST actions ([`admin.php:3775`](admin.php:3775), [`admin.php:3789`](admin.php:3789)).
    *   **Session Management:**
        *   Created a new "Gerir Sessões" admin section (`admin.php?section=sessions`).
        *   The page lists all active user sessions from the `sessions` table, displaying Session ID, Created At, Last Access, Expires At, and a summary of Cart Contents.
        *   Each session has a delete button. Deleting a session also removes its associated `download_tokens` (based on `session_id` in `download_tokens`). `order_access_tokens` are not deleted as they don't have a direct `session_id` link.
        *   Implemented `get_all_sessions_data()` ([`includes/session.php:435`](includes/session.php:435)) to fetch session details and `delete_session_and_associated_tokens()` ([`includes/session.php:470`](includes/session.php:470)) for deletion logic in [`includes/session.php`](includes/session.php:1).
        *   Created the view template [`templates/backend/sessions_list.php`](templates/backend/sessions_list.php:1).
        *   Updated [`admin.php`](admin.php:1) to handle the new "sessions" section: data preparation in `prepare_admin_view_data` ([`admin.php:565`](admin.php:565)), template inclusion for GET requests in the main switch ([`admin.php:5692`](admin.php:5692)) and AJAX switch ([`admin.php:1181`](admin.php:1181)), and POST action handling for deletion ([`admin.php:4152`](admin.php:4152)).
        *   Added "Gerir Sessões" link to the admin sidebar in [`templates/backend/partials/header.php`](templates/backend/partials/header.php:371).
        *   Debugged and fixed issues related to loading the "Gerir Sessões" page, including an `ArgumentCountError` with `session_decode()` and ensuring correct data preparation for AJAX navigation.
*   **UI: Floating Buttons Overlap Fix:**
    *   Resolved overlap between the product filter toggle button ([`templates/frontend/partials/collapsible_filters.php`](templates/frontend/partials/collapsible_filters.php)) and the "scroll-to-top" button ([`templates/frontend/partials/footer.php`](templates/frontend/partials/footer.php)).
    *   Adjusted Tailwind CSS positioning classes (`bottom-24`, `right-4`, `md:right-8`) for the "scroll-to-top" button to ensure vertical alignment and sufficient spacing from the filter button across screen sizes.
*   **UI: Filter Bar State Persistence:**
    *   Implemented functionality for the collapsible product filter bar ([`templates/frontend/partials/collapsible_filters.php`](templates/frontend/partials/collapsible_filters.php)) to remember its visibility state (expanded/collapsed) across page loads.
    *   Utilized browser `localStorage` to store and retrieve the state. JavaScript was updated to apply the saved state on `DOMContentLoaded` and update `localStorage` on toggle.
*   **Simple Product Add to Cart Validation Fix:**
    *   Resolved an issue where attempting to add a simple product (type 'regular' or 'digital') with custom fields to the cart would incorrectly trigger a server-side error "Por favor, selecione as opções do produto."
    *   Modified the `add_to_cart` action in [`includes/ajax_handler.php`](includes/ajax_handler.php:287) to correctly identify 'regular' and 'digital' product types.
    *   For these product types, the server-side logic now bypasses the requirement for a `variation_id` and proceeds to add the item with its custom fields, if all *required* custom fields are correctly submitted.
    *   Removed a duplicated and misplaced block of variation handling code within the `add_to_cart` case that was causing this issue and a syntax error.
*   **Simple Product Custom Field Stock Issue Fix:**
    *   Resolved an issue on the product detail page ([`templates/frontend/product_detail.php`](templates/frontend/product_detail.php:1)) where selecting a custom field for a simple product (one without variations) would incorrectly cause the product to appear as "Esgotado" (Out of stock).
    *   The JavaScript function `updateVariationDetails()` in [`templates/frontend/product_detail.php`](templates/frontend/product_detail.php:1105) was modified to correctly handle simple products (both physical and digital) by:
        *   Ensuring stock status for simple physical products is based on their actual stock (`window.simpleProductStock`) and not affected by variation logic.
        *   Maintaining "Download Imediato" status for digital products.
        *   Correctly enabling/disabling the "Add to Cart" button based on actual stock (for simple physical) and completion of *required* custom fields (for both simple physical and digital).
*   **Digital File Upload & Editing Fixes:**
    *   Resolved "Erro ao criar registro do arquivo digital" during new digital file uploads. This was due to a missing `display_name` column in the `digital_files` table.
        *   Added a migration script (`add_display_name_to_digital_files.php`) to add the `display_name` column.
        *   Integrated this migration into `db.php`.
    *   Resolved "Erro ao guardar o produto digital: Falha ao remover associações de tipos de ficheiro existentes" when editing digital products. This was due to an old database migration (`remove_filepath_from_digital_products.php`) potentially leaving an inconsistent state where SQLite was looking for a non-existent `digital_products_old` table during foreign key operations on `digital_product_file_type_associations`.
        *   Created and integrated a migration (`recreate_digital_product_file_type_associations.php`) to drop and correctly recreate the `digital_product_file_type_associations` table, resolving the foreign key issue.
    *   Fixed an issue where newly saved file type associations for digital products were not persisting. This was caused by the `recreate_digital_product_file_type_associations` migration running on every page load, wiping the associations. The call to this migration in `db.php` was commented out after its initial successful run.
    *   Enhanced logging in `digital_files_functions.php` and `db.php` to better diagnose database insertion issues.
*   **Digital File Display Names:** Implemented a `display_name` for digital files, allowing administrators to assign user-friendly names instead of relying on potentially cryptic original filenames. This involved:
    *   Adding a `display_name` column to the `digital_files` table (as part of the fix above).
    *   Updating migration scripts to include this column and populate defaults.
    *   Modifying file upload logic in `admin.php` (for `section=products`) and in `templates/backend/digital_files_management.php` to handle saving the `display_name`.
    *   Updating `create_digital_file` and `update_digital_file` functions in `includes/digital_files_functions.php`.
    *   Updating templates (`change_digital_file.php`, `digital_product_form.php`, `digital_files_management.php`) to display and allow editing of `display_name`.
    *   Removing the redundant `file_path` column from the `digital_products` table and updating relevant functions to fetch the path from `digital_files`.
*   **License Deletion Fix:** Completely redesigned license deletion to use direct links with browser confirmation dialogs instead of modals, eliminating the need for page refresh before deletion.
*   **"Alterar Arquivo" Redirect Fix:** Fixed issue where clicking "Alterar Arquivo" in digital product editing redirected to dashboard by implementing parameter consistency checks and ensuring both parameter formats are supported.
*   **Coupon System:** Fully implemented with admin management, frontend integration (cart/checkout), AJAX application, order integration, and various coupon types/rules.
*   **License & Download Validity Display:** Improved clarity by separating license status from download validity in UI and messaging.
*   **Maintenance Section:** Refactored to use simpler direct form submissions instead of complex AJAX/modals, improving reliability. Added confirmation dialogs. Fixed DB backup constant and flash message HTML rendering.
*   **Admin Filters & Pagination:** Implemented for Pages and Blog Posts sections.
*   **Messages Section (Backoffice):** Redesigned UI, fixed delete functionality using simple form submission and JS confirmation (removed AJAX/modal).
*   **Order Status Handling for Digital Products:** Comprehensive license status management tied to order status changes (processing, shipped/completed, cancelled, refunded), including download token removal and email notifications.
*   **License Management Enhancements:** Added features to reset download counts, edit expiration dates, delete download history, and toggle license activation/deactivation in admin.
*   **Digital Products Implementation (Core):** Completed including forms, file uploads, frontend display, secure downloads, and admin statistics.
*   **Stock Management (Non-Digital):** Implemented stock reduction on order completion and restoration for cancelled/refunded orders, with tracking and admin tools.
*   **Order Success Page:** Context-aware display (initial vs. revisit), data censoring on revisits, email verification for data reveal, option to resend order details.
*   **Prior Fixes:** License email verification, dashboard JS issues with AJAX nav, placeholder links, min order/free shipping, product videos, custom fields enhancements, product SEO/social meta, blog post detail page enhancements, blog publishing with OG images, homepage blog slider, sitemap/XML generation, contact page redesign, download statistics customer data decryption.
*   **Admin Product Form: Creation Date Save Fix:**
    *   Resolved an issue where the `created_at` date was not being saved correctly when editing a product if the product was of type 'digital' or 'variation'.
    *   Modified [`admin.php`](admin.php:1) in the product update logic (around lines [`admin.php:2091`](admin.php:2091) and [`admin.php:2122`](admin.php:2122)) to ensure the `created_at` field is conditionally included in the SQL `UPDATE` statement for all product types if a valid new date is provided via `$_POST['created_at']`.
    *   Corrected the help text in [`templates/backend/product_form.php`](templates/backend/product_form.php:142) to guide users to input the date in `YYYY-MM-DDTHH:MM` format, aligning with `datetime-local` input and backend parsing.
*   **Blog: "CODE" Post Type Implementation:**
    *   Added a new blog post type "CODE" to allow embedding and execution of PHP, HTML, and JavaScript.
    *   **Database:**
        *   Created migration [`includes/db_migrations/add_code_blog_post_type.php`](includes/db_migrations/add_code_blog_post_type.php:1) to:
            *   Add `code_content` TEXT column to `blog_posts` table.
            *   Update `CHECK` constraint on `post_type` in `blog_posts` to include 'CODE'.
        *   Integrated the migration into [`includes/db.php`](includes/db.php:1).
    *   **Admin Interface ([`templates/backend/blog_post_form.php`](templates/backend/blog_post_form.php:1)):**
        *   Added "CODE" (Código Executável) to the post type dropdown.
        *   Added a textarea for `code_content` input, visible only when "CODE" type is selected.
        *   Standard content/link fields are hidden for "CODE" type.
    *   **Backend Logic ([`admin.php`](admin.php:1), [`includes/blog_functions.php`](includes/blog_functions.php:1)):**
        *   Modified `admin.php` to handle `code_content` during saving/updating blog posts.
        *   Updated `add_blog_post()` and `update_blog_post()` in `includes/blog_functions.php` to save/update `code_content` and nullify other content fields appropriately based on `post_type`.
    *   **Frontend Rendering ([`templates/frontend/blog_post.php`](templates/frontend/blog_post.php:1)):**
        *   Implemented logic to execute `code_content` using `eval()` when `post_type` is 'CODE'.
        *   Added a prominent security warning on the frontend when displaying "CODE" posts.
        *   Included basic `try-catch` around `eval()` to display execution errors and log them.
    *   **Security Implication:** This feature uses `eval()` and carries significant security risks. It should be used with extreme caution.

## Next Steps (Based on Progress Documentation)
1.  **Testing Recently Implemented Token & Session Management:**
    *   Verify "Limpar Tokens de Download ATIVOS (Não Expirados)" works as expected.
    *   Verify "Limpar Tokens de Acesso ATIVOS (Não Expirados)" works as expected.
    *   Thoroughly test the "Gerir Sessões" page: listing accuracy, cart summary display, session deletion, and associated download token deletion.
2.  **Testing Recently Fixed Product Add to Cart Functionality (Simple & Digital Products with Custom Fields):**
    *   Thoroughly test adding simple products (physical) with custom fields to the cart: ensure correct server-side validation (no "select options" error), stock is handled, and custom fields are saved with the cart item.
    *   Thoroughly test adding digital products with custom fields to the cart: ensure correct server-side validation and custom fields are saved.
3.  **Testing Recently Fixed Product Detail Page Functionality (Simple & Digital Products):**
    *   Thoroughly test simple products (physical) with custom fields: ensure stock remains correct and "Add to Cart" enables/disables based on required fields and stock.
    *   Thoroughly test digital products with custom fields: ensure "Download Imediato" status remains and "Add to Cart" enables/disables based on required fields.
    *   Verify price updates correctly for all product types when custom fields are interacted with.
4.  **Testing Recently Fixed Digital Product Functionality:**
    *   Thoroughly test new digital file uploads via "Arquivos Digitais" section.
    *   Verify `display_name` creation and editing.
    *   Thoroughly test editing existing digital products: changing expiry/download limits, changing associated digital files, and especially changing/saving "Tipos de Arquivo Incluídos". Confirm these associations persist correctly.
    *   Test edge cases and error handling for all digital product management features.
5.  **Testing & Enhancements for Admin Filters & Navigation:**
    *   Test with large datasets, various filter combos, edge cases.
    *   Consider adding column sorting, "remember filters" feature, advanced date range filters.
    *   Consider global search.
    *   Apply pattern to other admin list views (e.g., Products).
6.  **Testing Enhanced Order Status Handling (Digital Products):**
    *   Verify license status changes for all order statuses.
    *   Test email notifications thoroughly.
    *   Test mixed orders (digital & physical).
    *   Verify error handling and transaction management.
7.  **Testing Enhanced License Management Functionality:**
    *   Test reset downloads, expiry editing, history deletion, activation/deactivation toggle.
    *   Test in various scenarios (edge cases, errors).
    *   Verify error handling and user feedback.
8.  **Testing Digital Products Implementation (End-to-End):**
    *   Full workflow: creation to download.
    *   License activation/expiration.
    *   Download limits and security measures (IP restrictions, tokens).
    *   Unauthorized access prevention.
9.  **Enhance Security for Digital Products:**
    *   Consider watermarking for applicable file types.
    *   Review and potentially enhance IP-based restrictions.
    *   Consider additional encryption for sensitive files.
10. **Create Documentation for Digital Products:**
    *   Admin guide for managing digital products and licenses.
    *   Customer help pages for accessing purchases and troubleshooting.
11. **Future Enhancements (Digital Products):**
    *   Subscription-based digital products.
    *   Preview/sample downloads.
    *   Detailed analytics.
    *   Product bundles.
    *   Batch operations for license management.
12. **Testing & Enhancements for Other Recently Implemented Features:** Placeholder links, custom fields (run cleanup script, test file cleanup), product SEO, blog slider, sitemap, contact page.
13. **Testing "CODE" Blog Post Type:**
    *   Verify that PHP, HTML, and JavaScript execute correctly from the `code_content` field.
    *   Test admin interface for creating/editing "CODE" posts.
    *   Ensure data integrity (e.g., other content fields are nulled for "CODE" posts).
    *   Observe error handling for `eval()` on the frontend and in logs.
    *   Confirm security warnings are displayed appropriately.

## Active Decisions & Considerations
*   **Cookieless Architecture:** Fundamental constraint. All new features must comply.
*   **Simplicity vs. AJAX/Modals:** Continue to favor simpler, direct form submissions and page refreshes for core operations, especially in admin. AJAX is acceptable for enhancements like coupon application if it improves UX and aligns with preferences (e.g., auto-refresh after AJAX action). The admin sidebar navigation uses AJAX; ensure new sections are properly handled in both direct GET and AJAX GET request flows in `admin.php`.
*   **Security First:** Maintain high security standards (input validation, encryption, secure tokens, protection against common vulnerabilities, CSRF protection).
*   **Thorough Root Cause Analysis:** Continue this practice for bug fixing.
*   **Incremental Implementation:** Continue breaking down features into smaller steps.
*   **Code Cleanliness:** Regularly remove debug statements and unnecessary comments. Explore automation for cleanup.
*   **Database Interactions:** Prefer `UPDATE` over `INSERT` for existing records. Implement garbage collection. Use explicit user confirmations for destructive DB operations. Database migrations should be designed to run once or be idempotent. Schema checks (e.g., using `sqlite3` CLI) are useful before implementing DB-dependent logic.
*   **User Preferences:** Actively integrate user preferences (UI text, confirmations, no modals for deletes, etc.) into all new development and refactoring.
*   **`eval()` Security for "CODE" Posts:** The use of `eval()` for the "CODE" blog post type is a user-requested feature with acknowledged high security risks. Access to create/edit these posts should be strictly limited. The frontend displays a prominent warning. This feature is to be used with extreme caution.

## Important Patterns and Preferences (User-Driven)
*   **Root Cause Analysis:** Mandatory for bug fixing.
*   **Code Cleanup:** Remove `error_log`, `console.log`, comments; automate if possible.
*   **UI - Confirmations (Deletes):** Simple JS confirmation boxes (not modals), then page refresh. (e.g., Messages section, Maintenance, Session deletion).
*   **UI - Confirmations (DB Modify):** JS confirmation dialogs before permanent DB changes. (e.g., Maintenance section).
*   **UI - Readability:** Darker text on white backgrounds for client names/emails, coupon inputs. High contrast in admin dashboard. Display names for digital files for clarity.
*   **UI - Simplicity (Modals/AJAX):** Generally remove/avoid in favor of simpler, more reliable implementations, especially for admin CRUD. Incremental steps. Admin sidebar navigation is an exception and uses AJAX.
    *   *Exception*: Coupon application AJAX is used but is followed by a page refresh, aligning with "checkout page to automatically refresh when coupons are applied."
*   **Responsiveness:** Ensure all new UI elements are responsive.
*   **Cookieless Operation:** Non-negotiable.
*   **NIF Field:** 'PT' prefix, users enter numerics only.
*   **URL Obfuscation:** For `order_id`.

## Learnings & Insights
*   **Blocking Operations in UI Workflows:** Synchronous operations that involve file system operations (like `cleanup_orphaned_banner_images()`) should not be called directly within user-facing workflows like banner updates/deletions. These operations can cause the UI to hang or display blank pages, severely impacting user experience. Instead, such operations should be moved to standalone scripts that can be run independently or scheduled as maintenance tasks.
*   **File System Operations Performance:** Operations that scan directories and compare file lists can be time-intensive, especially with larger numbers of files. The banner cleanup function was scanning the upload directory and comparing against database records, which caused performance issues when called synchronously during user actions.
*   **Standalone Cleanup Scripts:** Creating dedicated cleanup scripts (like `simple_cleanup.php`) provides better control over maintenance operations and allows for manual execution when needed, without blocking the main application workflow. These scripts can be more robust and provide detailed feedback about their operations.
*   **AJAX Navigation in Admin:** When adding new admin sections, ensure they are handled in both the standard GET request routing and the AJAX GET request routing within `admin.php`. This includes preparing necessary view data in `prepare_admin_view_data` and having a corresponding `case` in the AJAX `switch` statement to include the correct template. Failure to do so can result in the new section falling back to the dashboard or not loading its content correctly.
*   **Session Data Parsing:** Parsing raw PHP session data strings (e.g., from the `sessions.data` column) requires care. Functions like `session_decode()` operate on the global `$_SESSION` and expect a specific format. For inspecting arbitrary session strings, especially to extract specific keys like 'cart' without disrupting the current session, manual parsing or careful, isolated unserialization attempts are needed. Ensure error handling (e.g., with `@unserialize()`) and robust parsing logic to avoid fatal errors if session data is empty, malformed, or not in the expected format.
*   **Database Schema Verification:** Before implementing features that depend on specific database column (e.g., `session_id` in token tables for cascading deletes), it's crucial to verify the actual schema using tools like the `sqlite3` CLI. Assumptions can lead to incorrect logic or runtime errors. For instance, `order_access_tokens` was found not to have a `session_id` column, requiring adjustment to the session deletion logic.
*   **Server-Side Validation Alignment**: Server-side validation (e.g., in AJAX handlers like `add_to_cart`) must accurately reflect the product structure. If client-side logic allows adding simple products with only custom fields (no variations), server-side validation must not erroneously require variation options for these product types. This prevents misleading error messages and ensures a consistent user experience.
*   **JavaScript Logic for Product Types**: When implementing dynamic UI updates on product pages (e.g., price, stock, add-to-cart button state), it's crucial that the JavaScript logic correctly distinguishes between different product types (simple, digital, variation). A single update function needs clear conditional branches to handle each type appropriately, especially when custom fields interact with these states. Failure to do so can lead to incorrect states (e.g., simple product shown as out of stock when a custom field is selected).
*   **Database Migrations:** Need to be carefully managed. Migrations that perform structural changes (like table recreation) should ideally be idempotent or have a mechanism to run only once (e.g., by checking schema or a flag). Unconditional re-runs on every DB connection can lead to data loss or unexpected behavior. The `display_name` column addition and the `digital_product_file_type_associations` table recreation highlighted this.
*   **Direct Links vs. JavaScript for Critical Operations:** Using direct links with native browser confirmation dialogs for operations like deletion is more reliable than complex JavaScript/modal approaches, especially in admin interfaces with AJAX navigation.
*   **URL Parameter Consistency:** Maintaining consistent parameter naming across the application is crucial. When parameter names change, implement backward compatibility by checking for both old and new names. Include all possible parameter variations in form submissions to ensure robustness.
*   **Simpler is Often Better:** Refactoring Maintenance and Messages sections from AJAX/modals to direct forms improved reliability and aligned with user preference.
*   **Digital Product Complexity:** Requires careful attention to license states, download security, order status integration, and clear UI/UX for both admin and customers. Clear separation of license status and download validity is crucial. Using descriptive `display_name` for files improves admin clarity. File type associations must be reliably saved.
*   **Cookieless Challenges:** Requires robust server-side or database-driven tracking mechanisms for visit-specific UI and security.
*   **User Preferences Drive Design:** Actively incorporating user preferences (e.g., simple confirmations, no modals for deletes, persistent UI states like filter visibility) leads to more satisfactory outcomes.
*   **Thorough Testing is Key:** Especially for complex interactions like order status changes affecting digital licenses and stock, and for database schema changes. Also, for responsive UI elements and client-side state persistence.
*   **Database Integrity:** Transaction management is vital for operations affecting multiple tables. Foreign key integrity is crucial and can be affected by migrations; careful handling (e.g., disabling/enabling FKs, recreating tables) is sometimes needed. The `digital_products_old` issue was a symptom of this.
*   **Clear Communication:** Accurate and distinct messaging for users (e.g., license vs. download expiration) prevents confusion.
*   **Consistent Data Processing:** Implementing consistent data processing for encrypted data (like customer information) ensures proper display across all admin interfaces and prevents confusion from seeing encrypted data.
*   **Logging for Debugging:** Detailed logging (SQL queries, parameters, results, error messages) is invaluable for diagnosing issues, especially with database interactions and AJAX requests. Reviewing error logs (e.g., `logs/error.log`) is a key step in troubleshooting.
*   **Client-Side State Management:** For simple UI state persistence (like filter visibility), `localStorage` provides a straightforward solution. Ensure state restoration on page load is robust, considering DOM readiness for accessing element properties like `scrollHeight`.
*   **CSS Specificity and Fixed Positioning:** When dealing with multiple `fixed` position elements (like floating action buttons), careful management of CSS (e.g., Tailwind utility classes for `bottom`, `right`, `z-index`) is needed to prevent overlaps and ensure consistent alignment across screen sizes. Iterative adjustments are often required.
*   **Code Execution from Database (`eval()`):** The implementation of the "CODE" blog post type relies on `eval()` to execute content stored in the `blog_posts.code_content` field. This is inherently dangerous due to the risk of Remote Code Execution (RCE) if malicious code is injected. While implemented as per request, it underscores the need for strict input control, limited access to this feature, and thorough code vetting for any content intended for execution. Error handling around `eval()` should be robust to prevent full script termination and provide some insight into issues, but it cannot mitigate the fundamental security risks.