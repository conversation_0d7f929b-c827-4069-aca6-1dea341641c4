<?php
// Test script to verify Pinterest CSV generation
header('Content-Type: text/plain; charset=utf-8');

// Include the get_products.php logic
$db_path = __DIR__ . '/../../dbjcs2112ew.sqlite';

if (!file_exists($db_path)) {
    die('Database file not found');
}

// Create PDO connection
$pdo = new PDO('sqlite:' . $db_path);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Get ALL products for Pinterest CSV generation
$sql = "SELECT
            p.id,
            p.name_pt as name,
            p.slug,
            p.description_pt as description,
            p.base_price as price,
            p.product_type,
            p.seo_title,
            p.seo_description,
            p.seo_keywords,
            (SELECT pi2.filename FROM product_images pi2
             WHERE pi2.product_id = p.id AND pi2.is_default = 1
             LIMIT 1) as image_filename,
            (SELECT c2.name FROM categories c2
             JOIN product_categories pc2 ON c2.id = pc2.category_id
             WHERE pc2.product_id = p.id AND c2.is_active = 1
             ORDER BY c2.name ASC LIMIT 1) as primary_category
        FROM products p
        WHERE p.is_active = 1
        ORDER BY p.name_pt ASC";

$stmt = $pdo->prepare($sql);
$stmt->execute();
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Total products found: " . count($products) . "\n\n";

// Test CSV escaping function
function removeAccents($text) {
    if (!$text) return '';

    $accentMap = [
        'á' => 'a', 'à' => 'a', 'ã' => 'a', 'â' => 'a', 'ä' => 'a',
        'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
        'í' => 'i', 'ì' => 'i', 'î' => 'i', 'ï' => 'i',
        'ó' => 'o', 'ò' => 'o', 'õ' => 'o', 'ô' => 'o', 'ö' => 'o',
        'ú' => 'u', 'ù' => 'u', 'û' => 'u', 'ü' => 'u',
        'ç' => 'c',
        'ñ' => 'n',
        'Á' => 'A', 'À' => 'A', 'Ã' => 'A', 'Â' => 'A', 'Ä' => 'A',
        'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
        'Í' => 'I', 'Ì' => 'I', 'Î' => 'I', 'Ï' => 'I',
        'Ó' => 'O', 'Ò' => 'O', 'Õ' => 'O', 'Ô' => 'O', 'Ö' => 'O',
        'Ú' => 'U', 'Ù' => 'U', 'Û' => 'U', 'Ü' => 'U',
        'Ç' => 'C',
        'Ñ' => 'N'
    ];

    return str_replace(array_keys($accentMap), array_values($accentMap), $text);
}

function escapeCSV($value) {
    if (!$value) return '';

    // Convert to string and remove accents
    $value = removeAccents((string)$value);

    // Filter out problematic characters that can break CSV
    // Remove double quotes and single quotes/apostrophes completely
    $value = preg_replace('/["\']/u', '', $value);
    
    // Remove other potentially problematic characters
    $value = preg_replace('/[\r\n\t]/u', ' ', $value); // Replace newlines and tabs with spaces
    $value = preg_replace('/\s+/u', ' ', $value); // Replace multiple spaces with single space
    $value = trim($value); // Remove leading/trailing whitespace

    // If value contains comma, wrap in quotes (but we've already removed internal quotes)
    if (strpos($value, ',') !== false) {
        $value = '"' . $value . '"';
    }

    return $value;
}

// Test with products that might have problematic characters
$problematic_products = [];
foreach ($products as $product) {
    if (strpos($product['name'], '"') !== false || 
        strpos($product['name'], "'") !== false ||
        strpos($product['description'], '"') !== false || 
        strpos($product['description'], "'") !== false ||
        strpos($product['seo_description'], '"') !== false || 
        strpos($product['seo_description'], "'") !== false ||
        strpos($product['seo_keywords'], '"') !== false || 
        strpos($product['seo_keywords'], "'") !== false) {
        $problematic_products[] = $product;
    }
}

echo "Products with quotes/apostrophes found: " . count($problematic_products) . "\n\n";

if (count($problematic_products) > 0) {
    echo "Testing CSV escaping on problematic products:\n";
    echo "==========================================\n\n";
    
    foreach (array_slice($problematic_products, 0, 5) as $product) {
        echo "Product ID: " . $product['id'] . "\n";
        echo "Original name: " . $product['name'] . "\n";
        echo "Escaped name: " . escapeCSV($product['name']) . "\n";
        
        if ($product['seo_description']) {
            echo "Original SEO description: " . substr($product['seo_description'], 0, 100) . "...\n";
            echo "Escaped SEO description: " . substr(escapeCSV($product['seo_description']), 0, 100) . "...\n";
        }
        
        if ($product['seo_keywords']) {
            echo "Original SEO keywords: " . $product['seo_keywords'] . "\n";
            echo "Escaped SEO keywords: " . escapeCSV($product['seo_keywords']) . "\n";
        }
        
        echo "\n---\n\n";
    }
}

// Generate sample CSV content for first 10 products
echo "Sample CSV content (first 10 products):\n";
echo "=======================================\n\n";

$headers = [
    'Title',
    'Media URL',
    'Pinterest board',
    'Description',
    'Link',
    'Keywords'
];

echo implode(',', $headers) . "\n";

foreach (array_slice($products, 0, 10) as $product) {
    // Prepare data according to Pinterest requirements
    $title = escapeCSV(substr($product['name'], 0, 100)); // Max 100 chars

    $imageUrl = $product['image_filename'] ?
        'https://joaocesarsilva.com/public/assets/images/products/' . $product['image_filename'] :
        'https://joaocesarsilva.com/public/assets/images/no-image.png';

    // Board name: "JCS Studio - Category Name" or default if no category
    $board = 'JCS Studio';
    if ($product['primary_category'] && trim($product['primary_category'])) {
        $board = 'JCS Studio - ' . trim($product['primary_category']);
    }
    $board = escapeCSV($board);

    // Description: SEO description or regular description (max 500 chars)
    $description = $product['seo_description'] ?: $product['description'] ?: $product['name'];
    $description = escapeCSV(substr($description, 0, 500));

    $productUrl = 'https://joaocesarsilva.com/index.php?product=' . $product['slug'];

    // Keywords from SEO keywords
    $keywords = escapeCSV($product['seo_keywords'] ?: '');

    // Create CSV row
    $row = [
        $title,
        $imageUrl,
        $board,
        $description,
        $productUrl,
        $keywords
    ];

    echo implode(',', $row) . "\n";
}

echo "\nTest completed successfully!\n";
?>
